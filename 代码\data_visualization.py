import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib.font_manager import FontProperties
import sys

# 设置中文字体
try:
    # 尝试使用系统字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'FangSong', 'KaiTi']  # 添加更多中文字体
    
    # 如果系统字体不可用，尝试使用指定字体文件
    # font_path = "C:/Windows/Fonts/msyh.ttc"  # 微软雅黑字体路径
    # chinese_font = FontProperties(fname=font_path, size=12)
    
    plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题
except Exception as e:
    print(f"字体设置错误: {str(e)}")
    # 如果字体设置失败，使用默认字体
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Bitstream Vera Sans']

# 创建输出目录
output_dir = '数据可视化报告/图表输出'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 定义保存图表的函数
def save_plot(filename):
    # 使用bbox_inches='tight'来确保所有元素都包含在保存的图片中
    try:
        plt.savefig(os.path.join(output_dir, filename), dpi=300, bbox_inches='tight')
        print(f"图表已保存: {filename}")
    except Exception as e:
        print(f"保存图表时出错: {str(e)}")
    plt.close()

# 设置风格
sns.set_style("whitegrid")

# 1. 二手房数据
print("开始处理二手房数据...")

# 读取二手房数据
ershoufang_df = pd.read_excel("..\\数据集\\ershoufang.xlsx")

# 显示原始列名
print("二手房数据原始列名:", ershoufang_df.columns.tolist())

# 使用位置索引而不是列名
# 簇状柱形图：不同户型的数量分布
plt.figure(figsize=(12, 7))
try:
    type_counts = ershoufang_df.iloc[:, 1].value_counts().sort_values(ascending=False).head(10)  # 假设户型是第二列
    sns.barplot(x=type_counts.index, y=type_counts.values, palette="viridis")
    plt.title('二手房户型数量分布', fontsize=16)
    plt.xlabel('户型', fontsize=14)
    plt.ylabel('数量', fontsize=14)
    plt.xticks(rotation=45, fontsize=12)
    plt.yticks(fontsize=12)
    for i, v in enumerate(type_counts.values):
        plt.text(i, v + 5, str(v), ha='center', va='bottom', fontsize=12)
    save_plot('二手房户型数量分布.png')
except Exception as e:
    print(f"簇状柱形图生成错误: {str(e)}")

# 散点图：房屋面积与价格的关系
plt.figure(figsize=(12, 7))
try:
    area_col = 2  # 面积列的索引（从0开始）
    price_col = 5  # 总价列的索引
    sns.scatterplot(data=ershoufang_df, x=ershoufang_df.iloc[:, area_col], y=ershoufang_df.iloc[:, price_col], alpha=0.6, color='teal')
    plt.title('房屋面积与价格关系', fontsize=16)
    plt.xlabel('面积(平方米)', fontsize=14)
    plt.ylabel('价格(万元)', fontsize=14)
    plt.xticks(fontsize=12)
    plt.yticks(fontsize=12)
    save_plot('房屋面积与价格关系.png')
except Exception as e:
    print(f"散点图生成错误: {str(e)}")

# 箱形图：各行政区房价的分布情况
plt.figure(figsize=(14, 8))
try:
    area_col = 0  # 所在区列的索引
    price_col = 5  # 总价列的索引
    sns.boxplot(data=ershoufang_df, x=ershoufang_df.iloc[:, area_col], y=ershoufang_df.iloc[:, price_col], palette="magma")
    plt.title('各行政区房价分布', fontsize=16)
    plt.xlabel('行政区', fontsize=14)
    plt.ylabel('价格(万元)', fontsize=14)
    plt.xticks(rotation=45, fontsize=12)
    plt.yticks(fontsize=12)
    save_plot('各行政区房价分布.png')
except Exception as e:
    print(f"箱形图生成错误: {str(e)}")

# 2. 产品销售统计表
print("开始处理产品销售统计表...")

# 读取产品销售统计表
df_sales = pd.read_excel("..\\数据集\\sales.xlsx")

# 修复列名
try:
    df_sales.columns = ['订单日期', '产品名称', '销售数量', '销售单价', '销售额']
except:
    try:
        df_sales.columns = ['日期', '商品编码', '销售数量', '销售单价（元）', '销售额（元）']
    except Exception as e:
        print(f"列名修复失败: {str(e)}")

# 显示列名
print("产品销售统计表列名:", df_sales.columns.tolist())

# 折线图：销售额随时间的变化趋势
plt.figure(figsize=(12, 7))
# 假设有一个日期列，先将其转换为datetime类型
try:
    # 确保日期列是日期类型
    if '订单日期' in df_sales.columns:
        df_sales['订单日期'] = pd.to_datetime(df_sales['订单日期'])
        
        # 按30天聚合销售额
        monthly_sales = df_sales.resample('30D', on='订单日期').sum(numeric_only=True)
        x_data = monthly_sales.index
        y_data = monthly_sales['销售额']
    elif '日期' in df_sales.columns:
        df_sales['日期'] = pd.to_datetime(df_sales['日期'])
        
        # 按30天聚合销售额
        monthly_sales = df_sales.resample('30D', on='日期').sum(numeric_only=True)
        x_data = monthly_sales.index
        y_data = monthly_sales['销售额（元）']
    else:
        # 如果没有日期列，则使用原始数据
        x_data = df_sales.index
        if '销售额' in df_sales.columns:
            y_data = df_sales['销售额']
        else:
            y_data = df_sales['销售额（元）']

    print("x_data:", x_data)
    print("y_data:", y_data)

    sns.lineplot(x=x_data, y=y_data, marker='o', color='navy')
    plt.title('30天销售额趋势', fontsize=16)
    plt.xlabel('日期', fontsize=14)
    plt.ylabel('销售额', fontsize=14)
    plt.xticks(fontsize=12, rotation=45)
    plt.yticks(fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    # 添加数据标签
    for x, y in zip(x_data, y_data):
        plt.text(x, y + 0.05 * y, f'{y:.1f}', ha='center', va='bottom', fontsize=12,
                 bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    save_plot('30天销售额趋势.png')
except KeyError as e:
    print(f"错误：未找到列: {str(e)}，请检查实际数据列名。")
except Exception as e:
    print(f"折线图生成错误: {str(e)}")

# 环形图：各产品类别销售额占比
plt.figure(figsize=(10, 8))
try:
    # 假设产品类别是产品名称列
    if '产品名称' in df_sales.columns:
        category_sales = df_sales.groupby('产品名称')['销售额'].sum()
    else:
        product_col = df_sales.columns[1]  # 默认选第二列作为产品标识
        category_sales = df_sales.groupby(product_col)['销售额（元）'].sum()
    
    colors = sns.color_palette("pastel")
    wedges, texts = plt.pie(category_sales, startangle=90, colors=colors, wedgeprops=dict(width=0.35))
    
    # 添加图例和标签
    plt.legend(wedges, category_sales.index,
              title="产品类别",
              loc="center left",
              bbox_to_anchor=(1, 0, 0.5, 1),
              prop={'size': 12},
              fancybox=True,
              shadow=True,
              edgecolor='black')
    plt.setp(texts, visible=False)  # 隐藏默认标签
    plt.title('各产品类别销售额占比', fontsize=16)
    save_plot('各产品类别销售额占比.png')
except KeyError as e:
    print(f"错误：未找到列: {str(e)}，请检查实际数据列名。")
except Exception as e:
    print(f"环形图生成错误: {str(e)}")

# 热力图：不同产品在不同季度的销售矩阵
plt.figure(figsize=(14, 10))
try:
    # 提取季度信息
    if '订单日期' in df_sales.columns:
        df_sales['季度'] = df_sales['订单日期'].dt.quarter
    elif '日期' in df_sales.columns:
        df_sales['季度'] = df_sales['日期'].dt.quarter
    # 假设有'产品名称'列和'销售额'列
    if '产品名称' in df_sales.columns:
        pivot_table = df_sales.pivot_table(values='销售额', index='产品名称', columns='季度', aggfunc='sum', fill_value=0)
    else:
        product_col = df_sales.columns[1]  # 默认选第二列作为产品标识
        if '销售额' in df_sales.columns:
            sales_col = '销售额'
        else:
            sales_col = '销售额（元）'
        pivot_table = df_sales.pivot_table(values=sales_col, index=product_col, columns='季度', aggfunc='sum', fill_value=0)
    
    sns.heatmap(pivot_table, annot=True, fmt='.1f', cmap='YlGnBu', linewidths=.5)
    plt.title('不同产品在各季度的销售额', fontsize=16)
    plt.xlabel('季度', fontsize=14)
    plt.ylabel('产品名称', fontsize=14)
    plt.xticks(fontsize=12)
    plt.yticks(fontsize=12, rotation=0)
    save_plot('不同产品在各季度的销售额.png')
except KeyError as e:
    print(f"错误：未找到列: {str(e)}，请检查实际数据列名。")
except Exception as e:
    print(f"热力图生成错误: {str(e)}")

# 3. 国内生产总值季度数据
print("开始处理国内生产总值季度数据...")

# 读取GDP数据
try:
    gdp_df = pd.read_excel(r"D:/pythonProject4/数据可视化报告/数据集/国内生产总值季度数据.xlsx")
    # 显示原始列名
    print("原始GDP数据列名:", gdp_df.columns.tolist())
    # 跳过GDP数据处理，因为结构太复杂且没有增长率数据
    print("由于GDP数据结构复杂且缺少增长率数据，跳过GDP数据可视化。")
except FileNotFoundError:
    print("错误：文件未找到，请确认路径是否正确。")
except Exception as e:
    print(f"处理GDP数据时发生错误: {str(e)}")

# 最后，显示所有图表
# plt.show()